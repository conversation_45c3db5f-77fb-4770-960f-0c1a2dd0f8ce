"use client";

import { useState } from "react";
import { <PERSON><PERSON>, Button, Text, Card } from "@whop/react/components";
import { XIcon, ImageIcon } from "./Icons";

interface CreateCommunityModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CreateCommunityModal({ isOpen, onClose }: CreateCommunityModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    isPrivate: false,
    tags: "",
    image: null as File | null
  });

  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, image: file }));
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = () => {
    console.log("Creating community:", formData);
    onClose();
    // Reset form
    setFormData({
      name: "",
      description: "",
      isPrivate: false,
      tags: "",
      image: null
    });
    setImagePreview(null);
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Content className="max-w-2xl w-full max-h-[90vh] overflow-y-auto bg-white rounded-lg card-shadow">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <h2 className="text-h2 text-gray-900">
            Create Community
          </h2>
          <Button 
            variant="ghost" 
            size="1" 
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100"
          >
            <XIcon className="w-5 h-5" />
          </Button>
        </div>

        <div className="p-6 space-y-6">
          {/* Community Image */}
          <div>
            <h3 className="text-h3 text-gray-900 mb-3">
              Community Image
            </h3>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
              {imagePreview ? (
                <div className="relative">
                  <img 
                    src={imagePreview} 
                    alt="Preview" 
                    className="max-h-48 mx-auto rounded-lg"
                  />
                  <Button
                    variant="ghost"
                    size="1"
                    onClick={() => {
                      setImagePreview(null);
                      setFormData(prev => ({ ...prev, image: null }));
                    }}
                    className="absolute top-2 right-2 bg-black/50 text-white hover:bg-black/70 rounded-full p-1"
                  >
                    <XIcon className="w-4 h-4" />
                  </Button>
                </div>
              ) : (
                <label className="cursor-pointer">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                  <div className="flex flex-col items-center">
                    <ImageIcon className="w-12 h-12 text-gray-400 mb-3" />
                    <Text size="3" className="text-gray-600 mb-1">
                      Upload community image
                    </Text>
                    <Text size="2" className="text-gray-400">
                      PNG, JPG up to 10MB
                    </Text>
                  </div>
                </label>
              )}
            </div>
          </div>

          {/* Community Details */}
          <div className="space-y-4">
            <div>
              <h3 className="text-body font-medium text-gray-900 mb-2">
                Community Name
              </h3>
              <input
                type="text"
                placeholder="e.g., Crypto Traders Hub"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                className="w-full bg-gray-50 border border-gray-200 rounded-lg px-3 py-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                style={{borderWidth: '1px'}}
              />
            </div>

            <div>
              <h3 className="text-body font-medium text-gray-900 mb-2">
                Description
              </h3>
              <textarea
                placeholder="Describe what your community is about..."
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                rows={4}
                className="w-full bg-gray-50 border border-gray-200 rounded-lg resize-none px-3 py-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                style={{borderWidth: '1px'}}
              />
            </div>

            <div>
              <h3 className="text-body font-medium text-gray-900 mb-2">
                Privacy Setting
              </h3>
              <div className="flex gap-4">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="privacy"
                    checked={!formData.isPrivate}
                    onChange={() => handleInputChange("isPrivate", false)}
                    className="text-blue-600"
                  />
                  <span className="text-body text-gray-900">Public - Anyone can join</span>
                </label>
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="privacy"
                    checked={formData.isPrivate}
                    onChange={() => handleInputChange("isPrivate", true)}
                    className="text-blue-600"
                  />
                  <span className="text-body text-gray-900">Private - Invite only</span>
                </label>
              </div>
            </div>

            <div>
              <h3 className="text-body font-medium text-gray-900 mb-2">
                Tags
              </h3>
              <input
                type="text"
                placeholder="e.g., Crypto, Forex, Day Trading (comma separated)"
                value={formData.tags}
                onChange={(e) => handleInputChange("tags", e.target.value)}
                className="w-full bg-gray-50 border border-gray-200 rounded-lg px-3 py-3 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-gray-300 focus:bg-white transition-colors duration-200"
                style={{borderWidth: '1px'}}
              />
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-100">
          <Button 
            variant="ghost" 
            onClick={onClose}
            className="px-6 py-2.5 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg font-medium"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            className="px-6 py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium"
            style={{boxShadow: 'inset 0 1px 0 0 rgba(255, 255, 255, 0.1)'}}
          >
            Create Community
          </Button>
        </div>
      </Dialog.Content>
    </Dialog.Root>
  );
}
