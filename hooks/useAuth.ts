import { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'
import { getProfile, createProfile } from '../lib/database'
import type { Profile } from '../lib/supabase'
import type { User } from '@supabase/supabase-js'

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // For now, create a mock user for testing
    // In production, you'll integrate this with your actual auth system
    const mockUser = {
      id: '550e8400-e29b-41d4-a716-446655440000',
      email: '<EMAIL>',
      user_metadata: {
        full_name: 'Test User',
        avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face'
      }
    } as User;

    setUser(mockUser);
    loadProfile(mockUser.id);
  }, [])

  const loadProfile = async (userId: string) => {
    try {
      let userProfile = await getProfile(userId)

      // If profile doesn't exist, create one
      if (!userProfile) {
        userProfile = await createProfile({
          id: userId,
          username: 'testuser',
          full_name: 'Test User',
          avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
          verified: false,
          followers_count: 0,
          bio: null
        })
      }

      setProfile(userProfile)
    } catch (error) {
      console.error('Error loading profile:', error)
      // Create a fallback profile for testing
      setProfile({
        id: '550e8400-e29b-41d4-a716-446655440000',
        username: 'testuser',
        full_name: 'Test User',
        avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
        verified: false,
        followers_count: 0,
        bio: 'Test user for development',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { error }
  }

  const signUp = async (email: string, password: string, metadata?: any) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    })
    return { error }
  }

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    return { error }
  }

  const signInWithProvider = async (provider: 'google' | 'github' | 'discord') => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    })
    return { error }
  }

  return {
    user,
    profile,
    loading,
    signIn,
    signUp,
    signOut,
    signInWithProvider,
    isAuthenticated: !!user
  }
}
